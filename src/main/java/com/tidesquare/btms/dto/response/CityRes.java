package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.City;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class CityRes {
    private Long id;
    private String cityCode;
    private String name;

    public static CityRes fromEntity(City entity) {
        if (entity == null)
            return null;
        return CityRes.builder()
                .id(entity.getId())
                .cityCode(entity.getCityCode())
                .name(entity.getName())
                .build();
    }
}
