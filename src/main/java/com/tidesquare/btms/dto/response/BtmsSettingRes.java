package com.tidesquare.btms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.*;
import com.tidesquare.btms.entity.embeddable.BtmsSetting;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class BtmsSettingRes {
    private String url;

    private String adminUrl;

    private String emailDomain;

    private String[] emailDomainList;

    private JoinAuthType joinAuthType;

    private BookingAdminApprovalType bookingAdminApprovalType;

    @JsonProperty("isReceiptPrint")
    private boolean isReceiptPrint;

    @JsonProperty("isUse")
    private boolean isUse;

    private String applyStartYmd;

    private String applyEndYmd;

    // 비교견적사용여부
    @JsonProperty("isComparativePrice")
    private boolean isComparativePrice;

    // 최저가사용여부
    @JsonProperty("isLowestPrice")
    private boolean isLowestPrice;

    private ComparativePriceType comparativePriceType;

    // 거래처별 기본정렬 타입
    private AirSearchOrderBy airSearchOrderBy;

    @JsonProperty("isOfflineUse")
    private boolean isOfflineUse;

    private String csCallNumber1;

    private String csCallNumber2;

    private String csCallNumber3;

    @JsonProperty("isDocumentEvidenceFile")
    private boolean isDocumentEvidenceFile;

    @JsonProperty("isOnlyCorporateCard")
    private boolean isOnlyCorporateCard;

    @JsonProperty("isDocumentNumberUse")
    private boolean isDocumentNumberUse;

    @JsonProperty("isDocumentNumberRequired")
    private boolean isDocumentNumberRequired;

    private Integer documentNumberUseCount;

    private String documentNumberUseTitle;

    // 국내선 자동발권 설정 여부
    @JsonProperty("isDomesticAutoTicketing")
    private boolean isDomesticAutoTicketing;

    private MenuUse menuUse;

    private MenuPrimary menuPrimary;

    public static BtmsSettingRes fromEntity(BtmsSetting entity) {
        if (entity == null) {
            return null;
        }
        return BtmsSettingRes.builder()
                .url(entity.getUrl())
                .adminUrl(entity.getAdminUrl())
                .emailDomain(entity.getEmailDomain())
                .emailDomainList(entity.getEmailDomainList())
                .joinAuthType(entity.getJoinAuthType())
                .bookingAdminApprovalType(entity.getBookingAdminApprovalType())
                .isReceiptPrint(entity.isReceiptPrint())
                .isUse(entity.isUse())
                .applyStartYmd(entity.getApplyStartYmd())
                .applyEndYmd(entity.getApplyEndYmd())
                .isComparativePrice(entity.isComparativePrice())
                .isLowestPrice(entity.isLowestPrice())
                .comparativePriceType(entity.getComparativePriceType())
                .airSearchOrderBy(entity.getAirSearchOrderBy())
                .isOfflineUse(entity.isOfflineUse())
                .csCallNumber1(entity.getCsCallNumber1())
                .csCallNumber2(entity.getCsCallNumber2())
                .csCallNumber3(entity.getCsCallNumber3())
                .isDocumentEvidenceFile(entity.isDocumentEvidenceFile())
                .isOnlyCorporateCard(entity.isOnlyCorporateCard())
                .isDocumentNumberUse(entity.isDocumentNumberUse())
                .isDocumentNumberRequired(entity.isDocumentNumberRequired())
                .documentNumberUseCount(entity.getDocumentNumberUseCount())
                .documentNumberUseTitle(entity.getDocumentNumberUseTitle())
                .isDomesticAutoTicketing(entity.isDomesticAutoTicketing())
                .menuUse(entity.getMenuUse())
                .menuPrimary(entity.getMenuPrimary())
                .build();
    }
}
