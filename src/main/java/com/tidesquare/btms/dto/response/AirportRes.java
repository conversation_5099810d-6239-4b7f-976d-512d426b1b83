package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.Airport;
import com.tidesquare.btms.service.CityService;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class AirportRes {
    private String code;
    private String name;
    private CityRes city;

    public static AirportRes fromEntity(Airport entity) {
        if (entity == null)
            return null;
        return AirportRes.builder()
                .code(entity.getCode())
                .name(entity.getName())
                .city(CityRes.fromEntity(CityService.findByIdStatic(entity.getCityId())))
                .build();
    }
}
