package com.tidesquare.btms.controller.admin.travel.cancel_travel_overseas;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Travel_;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.BookingAirTravelerRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.TravelRepo;
import com.tidesquare.btms.repository.TravelerRepo;
import com.tidesquare.btms.service.email.EmailService;
import com.tidesquare.btms.service.kakao_talk.KakaoTalkService;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.Name;
import com.tidesquare.btms.service.stella.dto.cancel_prn.CancelBody;
import com.tidesquare.btms.service.stella.dto.cancel_prn.CancelInfo;
import com.tidesquare.btms.service.stella.dto.request.PassengerInfoReq;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.RequestContextController;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class CancelTravelOverseasHandler {

  @Inject
  private CompanyRepo companyRepo;

  @Inject
  private TravelerRepo travelerRepo;

  @Inject
  private TravelRepo travelRepo;

  @Inject
  private TravelService travelService;

  @Inject
  private StellaService stellaService;

  @Inject
  private BookingAirTravelerRepo bookingAirTravelerRepo;

  @Inject
  private BookingAirRepo bookingAirRepo;

  @Inject
  private EmailService emailService;

  @Inject
  private KakaoTalkService kakaoTalkService;

  @Inject
  private RequestContextController requestContextController;

  public void run(UserInfo userInfo, CancelTravelOverseasBody body, Long travelId) {
    this.cancelTravel(userInfo, body, travelId);

    /* #region send email and kakao */
    Thread.startVirtualThread(() -> {
      this.sendEmailAndKakaoTalk(userInfo.getCompanyId(), travelId);
    });
    /* #endregion */
  }

  private void cancelTravel(UserInfo userInfo, CancelTravelOverseasBody body, Long travelId) {

    Travel travel = travelRepo.findSingleTravel(travelId);

    if (travel.getIsApproval() != null && !travel.getIsApproval()) {
      throw new ApiException(Status.CONFLICT, ErrorCode.E409_CONFICT_CANCEL_PNR);
    }

    if (!travel.getCompany().getId().equals(userInfo.getCompanyId())) {
      throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_NOT_PERMISSION_CANCEL_PNR);
    }

    if (!TravelStatus.Requested.equals(travel.getStatus())) {
      throw new ApiException(Status.NOT_FOUND, ErrorCode.E404_TRAVEL_NOT_CANCEL_RESERVATION);
    }

    BookingAir bookingAir = bookingAirRepo.findByTravelId(travelId);

    List<BookingAirTraveler> bookingAirTravelers = bookingAirTravelerRepo.findByBookingAirId(bookingAir.getId());

    Thread.startVirtualThread(() -> {
      CancelBody cancelBodyRequest = mapCancelInfoRequests(body, bookingAirTravelers, bookingAir.getOrderID());
      this.stellaService.cancelPRN(cancelBodyRequest);
    });

    travel.setStatus(TravelStatus.Rejected);
    travel.setCancelDate(new Date());
    travel.setCancelUser(new User(userInfo.getUserId()));
    travel.setApprovalMemo(body.getApprovalMemo());
    travel.setIsApproval(false);
    Map<String, Object> updateValues = new HashMap<String, Object>() {
      {
        put(Travel_.STATUS, travel.getStatus());
        put(Travel_.CANCEL_DATE, travel.getCancelDate());
        put(Travel_.CANCEL_USER, travel.getCancelUser());
        put(Travel_.APPROVAL_MEMO, travel.getApprovalMemo());
        put(Travel_.IS_APPROVAL, travel.getIsApproval());
      }
    };

    travelRepo.update(travel.getId(), updateValues);
    this.travelService.insertTravelStatusHistory(userInfo, travel);
  }

  private CancelBody mapCancelInfoRequests(CancelTravelOverseasBody body, List<BookingAirTraveler> bookingAirTravelers, String orderId) {

    List<CancelInfo> cancelInfos = new ArrayList<>();

    List<PassengerInfoReq> passengerInfos = new ArrayList<>();
    for (int i = 0; i < bookingAirTravelers.size(); i++) {
      BookingAirTraveler bookingAirTraveler = bookingAirTravelers.get(i);
      PassengerInfoReq passengerInfo = PassengerInfoReq.builder()
          .passengerIdx(i + 1)
          .paxType("ADT")
          .name(Name.builder().firstName(bookingAirTraveler.getFirstName()).lastName(bookingAirTraveler.getLastName()).build())
          .build();
      if (bookingAirTraveler.getBirthday() != null) {
        String birthDay = bookingAirTraveler.getBirthday();
        passengerInfo.setDateOfBirth(birthDay.substring(0, 4) + "-" + birthDay.substring(4, 6) + "-" + birthDay.substring(6));
      }
      passengerInfos.add(passengerInfo);
    }

    CancelInfo cancelInfoRequest = CancelInfo.builder()
        .orderId(orderId)
        .actionCode("PC")
        .cancelReason(body.getApprovalMemo())
        .passengerInfos(passengerInfos)
        .build();

    cancelInfos.add(cancelInfoRequest);

    CancelBody cancelBodyRequest = CancelBody.builder()
        .orderId(orderId)
        .ssCode("BTMS")
        .cancelInfos(cancelInfos)
        .build();

    return cancelBodyRequest;
  }

  private void sendEmailAndKakaoTalk(Long companyId, Long travelId) {
    try {
      this.requestContextController.activate();

      Company company = this.companyRepo.findFetchParentById(companyId);
      if (company == null || company.getAirEmSetting() == null) {
        return;
      }
      Travel travel = this.travelRepo.findFetchBookingAirAndBookingAirScheduleAndBookingAirTravelerById(travelId);
      Traveler reserver = this.travelerRepo.findByTravelIdAndIsReserverTrue(travel.getId());

      this.emailService.sendAirApprovalOrRejectEmail(company, travel, reserver);
      this.kakaoTalkService.sendAirApprovalOrReject(company, travel, reserver);
    } finally {
      this.requestContextController.deactivate();
    }
  }
}
