package com.tidesquare.btms.controller.user.travel.get_travel_rule;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Builder;
import lombok.AllArgsConstructor;
import java.util.List;

import com.tidesquare.btms.dto.response.TravelRuleRes;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetTravelRuleRes {
    private List<TravelRuleRes> travelRuleItems;
}
