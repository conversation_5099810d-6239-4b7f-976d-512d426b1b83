package com.tidesquare.btms.controller.user.travel;

import com.tidesquare.btms.controller.user.travel.get_travel_overseas.GetTravelOverseasHandler;
import com.tidesquare.btms.controller.user.travel.get_travel_overseas.GetTravelRes;
import com.tidesquare.btms.controller.user.travel.get_travel_rule.GetTravelRuleHandler;
import com.tidesquare.btms.controller.user.travel.get_travel_rule.GetTravelRuleRes;
import com.tidesquare.btms.controller.user.travel.list_travel.ListTravelHandler;
import com.tidesquare.btms.controller.user.travel.list_travel.ListTravelQuery;

import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PathParam;

import java.util.List;
import java.util.Map;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.controller.user.travel.create_compare_schedule.CreateCompareScheduleBody;
import com.tidesquare.btms.controller.user.travel.create_compare_schedule.CreateCompareScheduleHandler;
import com.tidesquare.btms.controller.user.travel.create_travel_overseas.CreateTravelOverseasBody;
import com.tidesquare.btms.controller.user.travel.create_travel_overseas.CreateTravelOverseasHandler;
import com.tidesquare.btms.controller.user.travel.create_travel_overseas.CreateTravelOverseasRes;
import com.tidesquare.btms.controller.user.travel.get_colleague_travel_upcoming.GetColleagueTravelUpcomingHandler;
import com.tidesquare.btms.controller.user.travel.get_colleague_travel_upcoming.GetColleagueTravelUpcomingQuery;
import com.tidesquare.btms.controller.user.travel.get_compair_schedule_overseas.GetCompareAirScheduleHandler;
import com.tidesquare.btms.controller.user.travel.get_history.GetTravelStatusHistoryHandler;
import com.tidesquare.btms.controller.user.travel.get_invoice_overseas.GetInvoicesHandler;
import com.tidesquare.btms.controller.user.travel.get_invoice_overseas.GetInvoiceRes;
import com.tidesquare.btms.controller.user.travel.get_ticket_overseas.GetBookingAirTicketHandler;
import com.tidesquare.btms.controller.user.travel.search_travel_overseas.SearchTravelOverseasBody;
import com.tidesquare.btms.controller.user.travel.search_travel_overseas.SearchTravelOverseasHandler;
import com.tidesquare.btms.dto.response.BookingAirTicketRes;
import com.tidesquare.btms.dto.response.CompareAirScheduleRes;
import com.tidesquare.btms.dto.response.TravelRes;
import com.tidesquare.btms.dto.response.TravelStatusHistoryRes;
import com.tidesquare.btms.filter.UserBinding;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.response.SearchFareScheduleResponse;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("user/travel")
@UserBinding
@SecurityRequirement(name = "bearer")
public class UserTravelController {

    @Inject
    private CreateTravelOverseasHandler createTravelOverseasHandler;

    @Inject
    private SearchTravelOverseasHandler searchTravelOverseasHandler;

    @Inject
    private GetTravelOverseasHandler getTravelOverseasHandler;

    @Inject
    private GetTravelStatusHistoryHandler getTravelStatusHistoryHandler;

    @Inject
    private GetBookingAirTicketHandler getBookingAirTicketHandler;

    @Inject
    private GetCompareAirScheduleHandler getCompareAirScheduleHandler;

    @Inject
    private CreateCompareScheduleHandler createCompareScheduleHandler;

    @Inject
    private GetInvoicesHandler getInvoiceListHandler;

    @Inject
    private GetTravelRuleHandler getTravelRuleHandler;

    @Inject
    private ListTravelHandler listTravelHandler;

    @Inject
    private GetColleagueTravelUpcomingHandler getColleagueTravelUpcomingHandler;

    @POST
    @Path("")
    @Operation(summary = "Get List Travel")
    public ApiResponse<Map<String, Object>> list(@BeanParam ListTravelQuery query, @BeanParam ApiQueryPagination pagination) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.listTravelHandler.run(userInfo, query, pagination));
    }

    @GET
    @Path("/upcoming")
    @Operation(summary = "Get Colleague Travel Upcoming")
    public ApiResponse<List<TravelRes>> getColleagueTravelUpcoming(@Valid @BeanParam GetColleagueTravelUpcomingQuery query) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getColleagueTravelUpcomingHandler.run(userInfo, query));
    }

    @POST
    @Path("/overseas")
    @Operation(summary = "Create Travel Overseas")
    public ApiResponse<CreateTravelOverseasRes> createTravelOverseas(@Valid CreateTravelOverseasBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.createTravelOverseasHandler.run(userInfo, body));
    }

    @POST
    @Path("/overseas/search")
    @Operation(summary = "Search Travel Overseas")
    public ApiResponse<SearchFareScheduleResponse> search(@Valid SearchTravelOverseasBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.searchTravelOverseasHandler.run(userInfo, body));
    }

    @GET
    @Path("/overseas/{travelId}")
    @Operation(summary = "Get Travel Booking Complete")
    public ApiResponse<GetTravelRes> getBookingComplete(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getTravelOverseasHandler.run(userInfo, travelId));
    }

    @GET
    @Path("/{travelId}/histories")
    @Operation(summary = "Get Travel Status History")
    public ApiResponse<List<TravelStatusHistoryRes>> getHistories(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getTravelStatusHistoryHandler.run(userInfo, travelId));
    }

    @GET
    @Path("/overseas/{travelId}/tickets")
    @Operation(summary = "Get Booking Air Ticket")
    public ApiResponse<List<BookingAirTicketRes>> getTickets(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getBookingAirTicketHandler.run(userInfo, travelId));
    }

    @GET
    @Path("/overseas/{travelId}/compare-schedule")
    @Operation(summary = "Get Compare Air Schedule")
    public ApiResponse<List<CompareAirScheduleRes>> getCompareSchedules(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getCompareAirScheduleHandler.run(userInfo, travelId));
    }

    @POST
    @Path("/overseas/{travelId}/compare-schedule")
    @Operation(summary = "Create Compare Air Schedule")
    public ApiResponse<Void> createCompareSchedules(@Valid @PathParam("travelId") Long travelId, CreateCompareScheduleBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        this.createCompareScheduleHandler.run(userInfo, travelId, body);
        return ApiResponse.fromData(null);
    }

    @GET
    @Path("/overseas/{travelId}/invoices")
    @Operation(summary = "Get Invoices")
    public ApiResponse<GetInvoiceRes> getInvoices(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getInvoiceListHandler.run(userInfo, travelId));
    }

    @GET
    @Path("/overseas/{travelId}/rule")
    @Operation(summary = "Get Travel Rule")
    public ApiResponse<GetTravelRuleRes> getRule(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.getTravelRuleHandler.run(userInfo, travelId));
    }
}
