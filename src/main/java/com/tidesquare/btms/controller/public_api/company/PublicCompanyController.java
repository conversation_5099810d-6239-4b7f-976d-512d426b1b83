package com.tidesquare.btms.controller.public_api.company;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.public_api.company.get_by_domain.GetCompanyByDomainHandler;
import com.tidesquare.btms.controller.public_api.company.get_by_domain.GetCompanyByDomainQuery;
import com.tidesquare.btms.controller.public_api.company.get_by_domain.GetCompanyByDomainRes;
import com.tidesquare.btms.controller.public_api.company.get_by_site_code.GetCompanyBySiteCodeHandler;
import com.tidesquare.btms.controller.public_api.company.get_by_site_code.GetCompanyBySiteCodeQuery;
import com.tidesquare.btms.controller.public_api.company.get_by_site_code.GetCompanyBySiteCodeRes;
import com.tidesquare.btms.controller.public_api.company.get_children.GetGroupChildrenHandler;
import com.tidesquare.btms.controller.public_api.company.get_children.GetGroupChildrenRes;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;

@Path("public/company")
public class PublicCompanyController {

    @Inject
    private GetCompanyByDomainHandler getCompanyByDomainHandler;

    @Inject
    private GetCompanyBySiteCodeHandler getCompanyBySiteCodeHandler;

    @Inject
    private GetGroupChildrenHandler getGroupChildrenHandler;

    @GET
    @Path("/get-by-domain")
    @Operation(summary = "Get Company By Domain")
    public ApiResponse<GetCompanyByDomainRes> createContactRequest(@Valid @BeanParam GetCompanyByDomainQuery query) {
        return ApiResponse.fromData(this.getCompanyByDomainHandler.run(query));
    }

    @GET
    @Path("/get-by-site-code")
    @Operation(summary = "Get Company By Site Code")
    public ApiResponse<GetCompanyBySiteCodeRes> getCompanyBySiteCode(@Valid @BeanParam GetCompanyBySiteCodeQuery query) {
        return ApiResponse.fromData(this.getCompanyBySiteCodeHandler.run(query));
    }

    @GET
    @Path("/{companyId}/children")
    @Operation(summary = "Get Group Company")
    public ApiResponse<List<GetGroupChildrenRes>> getChildrenCompany(@NotNull @PathParam("companyId") Long companyId) {
        return ApiResponse.fromData(this.getGroupChildrenHandler.run(companyId));
    }
}
