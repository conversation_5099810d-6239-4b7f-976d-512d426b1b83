package com.tidesquare.btms.service.stella.dto.cancel_prn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class CancelBody {
    private String orderId;
    private List<CancelInfo> cancelInfos;
    private String ssCode;
}
