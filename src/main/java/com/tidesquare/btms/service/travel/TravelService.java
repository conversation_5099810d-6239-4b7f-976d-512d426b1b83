package com.tidesquare.btms.service.travel;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.constant.Constants;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.InflowBookingType;
import com.tidesquare.btms.constant.OPType;
import com.tidesquare.btms.constant.PnrDataHistoryType;
import com.tidesquare.btms.constant.TravelBookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.Airline;
import com.tidesquare.btms.entity.Airport;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.BookingHistory;
import com.tidesquare.btms.entity.BusinessTrip;
import com.tidesquare.btms.entity.City;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.Department;
import com.tidesquare.btms.entity.DocumentNumber;
import com.tidesquare.btms.entity.PnrDataHistory;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.TravelCity;
import com.tidesquare.btms.entity.TravelStatusHistory;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.TravelerMileageInfo;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.BookingAirScheduleRepo;
import com.tidesquare.btms.repository.BookingAirTravelerRepo;
import com.tidesquare.btms.repository.BookingHistoryRepo;
import com.tidesquare.btms.repository.BusinessTripRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.DocumentNumberRepo;
import com.tidesquare.btms.repository.PnrDataHistoryRepo;
import com.tidesquare.btms.repository.TravelAgencyUserRepo;
import com.tidesquare.btms.repository.TravelCityRepo;
import com.tidesquare.btms.repository.TravelRepo;
import com.tidesquare.btms.repository.TravelStatusHistoryRepo;
import com.tidesquare.btms.repository.TravelerMileageInfoRepo;
import com.tidesquare.btms.repository.TravelerRepo;
import com.tidesquare.btms.repository.WorkspaceRepo;
import com.tidesquare.btms.service.AirlineService;
import com.tidesquare.btms.service.AirportService;
import com.tidesquare.btms.service.CityService;
import static com.tidesquare.btms.service.stella.Constant.STELLA_NO_PNR_ERROR_CODE;

import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.Contact;
import com.tidesquare.btms.service.stella.dto.FareInfo;
import com.tidesquare.btms.service.stella.dto.PNRStatus;
import com.tidesquare.btms.service.stella.dto.response.BtmsAdditionalInfoRes;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.stella.dto.response.PassengerInfoRes;
import com.tidesquare.btms.service.stella.dto.response.SegmentRes;
import com.tidesquare.btms.utils.AirUtil;
import com.tidesquare.btms.utils.DateUtil;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@ApplicationScoped
public class TravelService {

    @Inject
    private TravelAgencyUserRepo travelAgencyUserRepo;

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private BusinessTripRepo businessTripRepo;

    @Inject
    private TravelRepo travelRepo;

    @Inject
    private TravelerRepo travelerRepo;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private BookingAirScheduleRepo bookingAirScheduleRepo;

    @Inject
    private BookingAirTravelerRepo bookingAirTravelerRepo;

    @Inject
    private TravelerMileageInfoRepo travelerMileageInfoRepo;

    @Inject
    private CompanyRepo companyRepo;

    @Inject
    private WorkspaceRepo workspaceRepo;

    @Inject
    private AirportService airportService;

    @Inject
    private AirlineService airlineService;

    @Inject
    private CityService cityService;

    @Inject
    private TravelCityRepo travelCityRepo;

    @Inject
    private DocumentNumberRepo documentNumberRepo;

    @Inject
    private PnrDataHistoryRepo pnrDataHistoryRepo;

    @Inject
    private TravelStatusHistoryRepo travelStatusHistoryRepo;

    @Inject
    private BookingHistoryRepo bookingHistoryRepo;

    @Inject
    private StellaService stellaService;

    public boolean checkUserIsReverserOfTravel(Long userId, Long travelId) {
        Traveler traveler = this.travelerRepo.findByTravelIdAndIsReserverTrue(travelId);
        return traveler != null && traveler.getTravelerUserId().equals(userId);
    }

    public boolean checkUserIsReverserOrTravelerOfTravel(Long userId, Long travelId) {
        return this.travelerRepo.checkUserIsReverserOrTravelerOfTravel(travelId, userId);
    }

    @Transactional(rollbackOn = Exception.class)
    public void savePnrInfo(UserInfo userInfo, PNRInfoRes pnrInfo, PnrDataHistoryType pnrDataHistoryType) {
        BookingAir bookingAir = this.bookingAirRepo.findByPnrNoAndNotEndTravel(pnrInfo.getPnr());
        if (bookingAir == null) { // if don't have booking with the same pnr within 1 year
            this.savePnrInfo(userInfo, pnrInfo, pnrDataHistoryType, null);
            return;
        }
        List<PassengerInfoRes> passengerInfos = pnrInfo.getRetrieveData().getPassengerInfos();
        List<BookingAirTraveler> bookingAirTravelers = this.bookingAirTravelerRepo.findByBookingAirId(bookingAir.getId());
        for (PassengerInfoRes passengerInfo : passengerInfos) {
            for (BookingAirTraveler bookingAirTraveler : bookingAirTravelers) {
                if (bookingAirTraveler.getFirstName() != null && bookingAirTraveler.getFirstName().equals(passengerInfo.getName().getFirstName())
                        && bookingAirTraveler.getLastName() != null && bookingAirTraveler.getLastName().equals(passengerInfo.getName().getLastName())) {
                    this.savePnrInfo(userInfo, pnrInfo, pnrDataHistoryType, bookingAir);
                    return;
                }
            }
        }
        this.savePnrInfo(userInfo, pnrInfo, pnrDataHistoryType, null);
    }

    @Transactional(rollbackOn = Exception.class)
    public void savePnrInfo(UserInfo userInfo, PNRInfoRes pnrInfo, PnrDataHistoryType pnrDataHistoryType, BookingAir bookingAir) {
        boolean isNew = bookingAir == null;
        Travel travel = isNew ? null : bookingAir.getTravel();

        List<JourneyRes> journeys = null;
        BtmsAdditionalInfoRes btmsAdditionalInfo = null;
        if (pnrInfo.getRetrieveData() != null) {
            journeys = pnrInfo.getRetrieveData().getJourneys();
            btmsAdditionalInfo = pnrInfo.getRetrieveData().getBtmsAdditionalInfo();
        }
        TravelAgencyUser manager = this.getManager(btmsAdditionalInfo);

        if (pnrInfo.getErrorCode().equals(STELLA_NO_PNR_ERROR_CODE) || journeys.getFirst().getPnrStatus().equals(PNRStatus.XX)) {
            boolean isSavePnrDataHistory = false;
            if (isNew) {
                throw new RuntimeException("pnr has cancelled, no need create new");
            }
            if (travel.getStatus() == null) {
                throw new RuntimeException("existedTravel has no status");
            }
            if (travel.getStatus().equals(TravelStatus.Requested) || travel.getStatus().equals(TravelStatus.Approved) || travel.getStatus().equals(TravelStatus.Completed)) {
                isSavePnrDataHistory = true;
                travel.setStatus(TravelStatus.Cancelled);
                this.travelRepo.update(travel);
                this.insertTravelStatusHistory(userInfo, travel);
            }

            isSavePnrDataHistory = isSavePnrDataHistory || pnrInfo.getErrorCode().equals(STELLA_NO_PNR_ERROR_CODE);

            if (isSavePnrDataHistory) {
                /* #region insert pnrDataHistory */
                PnrDataHistory pnrDataHistory = new PnrDataHistory();
                pnrDataHistory.setBookingAirId(bookingAir.getId());
                pnrDataHistory.setPnrData(pnrInfo.getRawData());
                pnrDataHistory.setPnrDataHistoryType(pnrDataHistoryType);
                pnrDataHistory.setManager(manager);
                pnrDataHistory.setCreateDate(new Date());
                this.pnrDataHistoryRepo.insert(pnrDataHistory);
                /* #endregion */
            }
            return;
        }

        Company company = this.getCompany(isNew ? null : travel.getCompany().getId(), pnrInfo);
        List<Contact> contacts = pnrInfo.getRetrieveData().getContacts();
        Contact contact = contacts != null && !contacts.isEmpty() ? contacts.getFirst() : null;
        List<PassengerInfoRes> passengerInfos = pnrInfo.getRetrieveData().getPassengerInfos();
        Date bookingDate = this.getBookingDate(journeys.getFirst().getProvider(), pnrInfo.getRawData());
        boolean isOverseas = this.getIsOverseas(journeys);
        boolean changeCompany = travel != null && !travel.getCompany().getId().equals(company.getId());
        List<BookingAirTraveler> bookingAirTravelers = isNew ? new ArrayList<>() : this.bookingAirTravelerRepo.findFetchTravelerMileageInfosByBookingAirId(bookingAir.getId());
        List<Long> bookingAirTravelerIds = bookingAirTravelers.stream().map(bookingAirTraveler -> bookingAirTraveler.getId()).toList();

        BusinessTrip businessTrip = this.upsertBusinessTrip(isNew ? null : travel.getBusinessTripId(), journeys);
        boolean changeTravelers = false;
        if (bookingAirTravelers.size() == passengerInfos.size()) {
            for (int i = 0; i < bookingAirTravelers.size(); i++) {
                BookingAirTraveler bookingAirTraveler = bookingAirTravelers.get(i);
                PassengerInfoRes passengerInfo = passengerInfos.get(i);
                if (!bookingAirTraveler.getFirstName().equals(passengerInfo.getName().getFirstName()) || !bookingAirTraveler.getLastName().equals(passengerInfo.getName().getLastName())) {
                    changeTravelers = true;
                    break;
                }
            }
        } else {
            changeTravelers = true;
        }
        List<Customer> customersTraveler = new ArrayList<>();
        if (!company.getId().equals(Constants.COMPANY_ID_WITHOUT_SITE_CODE) && (isNew || changeCompany || changeTravelers)) {
            customersTraveler = this.getCustomerTravelers(company, passengerInfos);
        }

        List<Traveler> reserverAndTravelers = isNew ? new ArrayList<>() : this.travelerRepo.findByTravelId(travel.getId());
        Traveler reserver = !reserverAndTravelers.isEmpty() && reserverAndTravelers.getFirst().isReserver() ? reserverAndTravelers.getFirst() : null;
        boolean changeReserver = contact.getName() != null && (reserver == null || !reserver.getName().equals(contact.getName().getFirstName() + contact.getName().getLastName()));
        Customer customerReserver = null;
        if (!company.getId().equals(Constants.COMPANY_ID_WITHOUT_SITE_CODE) && (isNew || changeCompany || changeReserver)) {
            customerReserver = this.getCustomerReserver(company, contact);
        }
        /* #region upsert travel */
        if (isNew) { // if new -> create new travel
            travel = new Travel();
            travel.setBusinessTripId(businessTrip.getId());
            travel.setTravelBookingType(TravelBookingType.Air);
            travel.setSeatZone("all");
        }
        travel.setTravelPlace(this.getTravelPlace(journeys));
        travel.setGroupNumber(btmsAdditionalInfo == null ? "" : btmsAdditionalInfo.getGroupNumber());
        travel.setIsGroup(btmsAdditionalInfo != null && !StringUtils.isNullOrEmpty(btmsAdditionalInfo.getGroupNumber()));
        if (company.getId().equals(Constants.COMPANY_ID_WITHOUT_SITE_CODE)) {
            travel.setWorkspaceId(Constants.WORKSPACE_ID_WITHOUT_SITE_CODE);
        } else if (isNew || changeCompany || changeTravelers) { // isNew or change company or change booking air travelers -> change workspace
            if (customersTraveler.getFirst() != null) {
                travel.setWorkspaceId(customersTraveler.getFirst().getWorkspace().getId());
            } else {
                Workspace workspace = this.workspaceRepo.findCreatedFirstByCompanyId(company.getId());
                travel.setWorkspaceId(workspace.getId());
            }
        }
        travel.setCompany(company);
        travel.setIsOverseas(isOverseas);
        if (!StringUtils.isNullOrEmpty(journeys.getFirst().getDepartureDate())) {
            travel.setDepartYmd(journeys.getFirst().getDepartureDate().substring(0, 10).replaceAll("-", ""));
        }
        if (!StringUtils.isNullOrEmpty(journeys.getLast().getArrivalDate())) {
            travel.setReturnYmd(journeys.getLast().getArrivalDate().substring(0, 10).replaceAll("-", ""));
        }
        travel.setTravelPersonnel(passengerInfos.size());
        if (isNew) {
            travel.setStatus(TravelStatus.Requested);
        } else if (travel.getStatus() == null) {
            throw new RuntimeException("existedTravel has no status");
        } else if (travel.getStatus().equals(TravelStatus.Rejected)) {
            travel.setStatus(TravelStatus.Requested);
            travel.setIsApproval(null);
        } else if (travel.getStatus().equals(TravelStatus.Cancelled)) {
            if (travel.getIsApproval() == null) {
                travel.setStatus(TravelStatus.Requested);
            } else if (travel.getIsApproval()) {
                travel.setStatus(TravelStatus.Approved);
            } else {
                travel.setStatus(TravelStatus.Requested);
                travel.setIsApproval(null);
                log.error("travel status is cancelled but isApproval = false");
            }
        }
        if (isNew) {
            this.travelRepo.insert(travel);
        } else {
            this.travelRepo.update(travel);
        }
        /* #endregion */

        /* #region Build booking air traveler */
        Map<String, BookingAirTraveler> bookingAirTravelersMap = bookingAirTravelers.stream()
                .collect(Collectors.toMap(bookingAirTraveler -> bookingAirTraveler.getFirstName() + "/" + bookingAirTraveler.getLastName(), bookingAirTraveler -> bookingAirTraveler));

        List<BookingAirTraveler> newBookingAirTravelers = new ArrayList<>();
        double totalReserveAmount = 0.0;
        for (int i = 0; i < passengerInfos.size(); i++) {
            PassengerInfoRes passengerInfo = passengerInfos.get(i);
            String key = passengerInfo.getName().getFirstName() + "/" + passengerInfo.getName().getLastName();
            BookingAirTraveler bookingAirTraveler = null;
            if (bookingAirTravelersMap.containsKey(key)) {
                bookingAirTraveler = bookingAirTravelersMap.get(key);
            } else {
                bookingAirTraveler = new BookingAirTraveler();
            }
            newBookingAirTravelers.add(bookingAirTraveler);
            // Lala hứa là name không null.
            bookingAirTraveler.setFirstName(passengerInfo.getName().getFirstName());
            bookingAirTraveler.setLastName(passengerInfo.getName().getLastName());
            bookingAirTraveler.setTitle(passengerInfo.getName().getTitle());
            bookingAirTraveler.setTravelerFirstName(passengerInfo.getName().getFirstName());
            bookingAirTraveler.setTravelerLastName(passengerInfo.getName().getLastName());
            if (company.getId().equals(Constants.COMPANY_ID_WITHOUT_SITE_CODE)) {
                bookingAirTraveler.setTraveler(null);
                bookingAirTraveler.setTravelerName(null);
            } else if (isNew || changeCompany || changeTravelers) {
                Customer customer = customersTraveler.get(i);
                if (customer != null) {
                    bookingAirTraveler.setTraveler(customer);
                    bookingAirTraveler.setTravelerName(customer.getName());
                    bookingAirTraveler.setEmail(customer.getEmail());
                    bookingAirTraveler.setCellPhoneNumber(customer.getCellPhoneNumber());
                } else {
                    bookingAirTraveler.setTraveler(null);
                    bookingAirTraveler.setTravelerName(null);
                }
            }
            if (passengerInfo.getGender() != null) {
                bookingAirTraveler.setGender(passengerInfo.getGender());
            }
            if (passengerInfo.getDateOfBirth() != null) {
                bookingAirTraveler.setBirthday(passengerInfo.getDateOfBirth().replaceAll("-", ""));
            }
            bookingAirTraveler.setNationalityCode(passengerInfo.getNationality());
            if (passengerInfo.getPassport() != null) {
                bookingAirTraveler.setPassportNumber(passengerInfo.getPassport().getDocumentNo());
                bookingAirTraveler.setPassportNation(passengerInfo.getPassport().getIssuedByCode());
                if (passengerInfo.getPassport().getExpirationDate() != null) {
                    bookingAirTraveler.setPassportLimitDate(passengerInfo.getPassport().getExpirationDate().replace("-", ""));
                }
            }

            double fareAmount = 0;
            double dc = 0; // Luôn bằng 0
            double fuelSurCharge = 0; // Luôn bằng 0
            double tax = 0;
            for (FareInfo fareInfo : passengerInfo.getFareInfos()) {
                fareAmount += fareInfo.getAirFare();
                tax += (fareInfo.getAirTax() + fareInfo.getFuelChg());
            }
            double commissionAmount = AirUtil.calculation(company, !isOverseas, fareAmount, dc, tax);
            double reserveAmount = fareAmount - dc + tax + commissionAmount;
            bookingAirTraveler.setFareAmount(fareAmount - dc);
            bookingAirTraveler.setTax(tax);
            bookingAirTraveler.setDcAmount(dc);
            bookingAirTraveler.setFuelSurcharge(fuelSurCharge);
            bookingAirTraveler.setCommissionAmount(commissionAmount);
            bookingAirTraveler.setReserveAmount(reserveAmount);

            totalReserveAmount += reserveAmount;
        }
        /* #endregion */

        /* #region upsert booking air */
        if (isNew) {
            bookingAir = new BookingAir();
            bookingAir.setTravel(travel);
            bookingAir.setPnrNo(pnrInfo.getPnr());
            bookingAir.setOtherPnrNo(pnrInfo.getPnr());
            bookingAir.setInflowBookingType(InflowBookingType.CRS);
            bookingAir.setBookingDate(bookingDate);
        }
        bookingAir.setGdsType(journeys.getFirst().getProvider());
        if (bookingAirTravelers.size() != passengerInfos.size()) {
            PNRInfoRes newPnrInfoRes = this.stellaService.GetPNRInfo(bookingAir.getPnrNo(), bookingAir.getGdsType(), null);
            bookingAir.setOrderID(newPnrInfoRes.getOrderId());
        } else {
            bookingAir.setOrderID(pnrInfo.getOrderId());
        }
        Date ticketDate = null;
        if (!StringUtils.isNullOrEmpty(journeys.getFirst().getAirlineLimitDate()) && !StringUtils.isNullOrEmpty(journeys.getFirst().getAirlineLimitTime())) {
            ticketDate = DateUtil.string2Date(journeys.getFirst().getAirlineLimitDate() + " " + journeys.getFirst().getAirlineLimitTime(), "yyyy-MM-dd HHmm");
        }
        Date viewTicketDate = null;
        if (!StringUtils.isNullOrEmpty(journeys.getFirst().getFareLimitDate())) {
            viewTicketDate = DateUtil.string2Date(journeys.getFirst().getFareLimitDate() + " 1600", "yyyy-MM-dd HHmm");
        }
        if (bookingAir.getGdsType().equals(GDSType.AMADEUS)) {
            if (ticketDate == null) {
                ticketDate = DateUtil.getAmadeusDate(pnrInfo.getRawData(), OPType.OPC, bookingDate);
            }
            if (viewTicketDate == null) {
                viewTicketDate = DateUtil.getAmadeusDate(pnrInfo.getRawData(), OPType.OPW, bookingDate);
            }
        } else {
            if (ticketDate != null) {
                viewTicketDate = DateUtil.string2Date(journeys.getFirst().getAirlineLimitDate() + " 1600", "yyyy-MM-dd HHmm");
                viewTicketDate = new Date(viewTicketDate.getTime() - 24 * 60 * 60 * 1000);
            } else {
                viewTicketDate = null;
            }
        }
        bookingAir.setTicketDate(ticketDate);
        if (bookingAir.getViewTicketDateModified() == null || !bookingAir.getViewTicketDateModified()) {
            bookingAir.setViewTicketDate(viewTicketDate);
        }
        bookingAir.setSectionType(journeys.getFirst().getJourneyType());
        if (!StringUtils.isNullOrEmpty(journeys.getFirst().getDepartureDate()) && !StringUtils.isNullOrEmpty(journeys.getLast().getArrivalDate())) {
            bookingAir.setStayTerm(String.valueOf(DateUtil.diffDays(journeys.getLast().getArrivalDate(), journeys.getFirst().getDepartureDate(), "yyyy-MM-dd'T'HH:mm:ss")));
        }
        Airline ticketAirline = this.airlineService.findOrInsertByCode(journeys.getFirst().getAirline());
        bookingAir.setTicketAirlineId(ticketAirline.getId());
        bookingAir.setOfficeId(this.getOfficeId(bookingAir.getInflowBookingType(), isOverseas, journeys));
        if (manager != null) {
            bookingAir.setManager(manager);
            Department department = manager.getDepartment();
            if (department != null) {
                if (department.getParentId() != null) {
                    bookingAir.setManagerDepartment(department.getParentId());
                } else {
                    bookingAir.setManagerDepartment(department.getId());
                }
            }
            bookingAir.setTicketer(manager);
        }
        bookingAir.setReserveAmount(totalReserveAmount);
        if (isNew) {
            this.bookingAirRepo.insert(bookingAir);
        } else {
            this.bookingAirRepo.update(bookingAir);
        }
        /* #endregion */

        /* #region delete, insert booking air traveler */
        if (!isNew) {
            this.travelerMileageInfoRepo.deleteByBookingAirTravelerIds(bookingAirTravelerIds);
            this.bookingAirTravelerRepo.delete(bookingAirTravelerIds);
        }
        for (BookingAirTraveler bookingAirTraveler : newBookingAirTravelers) {
            bookingAirTraveler.setId(null);
            bookingAirTraveler.setBookingAirId(bookingAir.getId());
            this.bookingAirTravelerRepo.insert(bookingAirTraveler);
            if (bookingAirTraveler.getTravelerMileageInfos() != null) {
                for (TravelerMileageInfo travelerMileageInfo : bookingAirTraveler.getTravelerMileageInfos()) {
                    travelerMileageInfo.setId(null);
                    travelerMileageInfo.setBookingAirTravelerId(bookingAirTraveler.getId());
                    this.travelerMileageInfoRepo.insert(travelerMileageInfo);
                }
            }
        }
        /* #endregion */

        /* #region delete reserver and traveler */
        if (!isNew) {
            this.travelerRepo.deleteByTravelId(travel.getId());
        }
        /* #endregion */

        /* #region insert reserver */
        if (reserver == null) {
            reserver = new Traveler();
            reserver.setTravelId(travel.getId());
            reserver.setReserver(true);
        }
        if (contact != null) {
            if (contact.getName() != null) {
                reserver.setName(contact.getName().getFirstName() + contact.getName().getLastName());
            }
            if (contact.getPhoneNumber() != null) {
                reserver.setCellPhoneNumber(contact.getPhoneNumber().replaceAll("[- ]", ""));
            }
        }
        if (company.getId().equals(Constants.COMPANY_ID_WITHOUT_SITE_CODE)) {
            reserver.setTravelerUserId(null);
            reserver.setAccountingCode(null);
        } else if (isNew || changeCompany || changeReserver) {
            if (customerReserver != null) {
                reserver.setAccountingCode(customerReserver.getAccountingCode());
                reserver.setTravelerUserId(customerReserver.getId());
            } else {
                reserver.setTravelerUserId(null);
                reserver.setAccountingCode(null);
            }
        }
        this.travelerRepo.insert(reserver);
        /* #endregion */

        /* #region insert travelers */
        List<Traveler> travelers = reserverAndTravelers.stream().filter(traveler -> !traveler.isReserver()).toList();
        Map<String, Traveler> travelersMap = travelers.stream().collect(Collectors.toMap(traveler -> traveler.getFirstName() + "/" + traveler.getLastName(), traveler -> traveler));
        for (BookingAirTraveler bookingAirTraveler : newBookingAirTravelers) {
            Traveler traveler = new Traveler();
            traveler.setTravelId(travel.getId());
            traveler.setReserver(false);
            traveler.setFirstName(bookingAirTraveler.getFirstName());
            traveler.setLastName(bookingAirTraveler.getLastName());
            traveler.setName(bookingAirTraveler.getTravelerName());
            if (bookingAirTraveler.getTraveler() != null) {
                traveler.setTravelerUserId(bookingAirTraveler.getTraveler().getId());
                if (!StringUtils.isNullOrEmpty(bookingAirTraveler.getTraveler().getAccountingCode())) {
                    traveler.setAccountingCode(bookingAirTraveler.getTraveler().getAccountingCode());
                } else {
                    String key = bookingAirTraveler.getFirstName() + "/" + bookingAirTraveler.getLastName();
                    Traveler oldTraveler = travelersMap.get(key);
                    if (oldTraveler != null) {
                        traveler.setAccountingCode(oldTraveler.getAccountingCode());
                    }
                }
            }
            traveler.setCellPhoneNumber(bookingAirTraveler.getCellPhoneNumber());
            traveler.setEmail(bookingAirTraveler.getEmail());
            this.travelerRepo.insert(traveler);
        }
        /* #endregion */

        /* #region insert pnrDataHistory */
        PnrDataHistory pnrDataHistory = new PnrDataHistory();
        pnrDataHistory.setBookingAirId(bookingAir.getId());
        pnrDataHistory.setPnrData(pnrInfo.getRawData());
        pnrDataHistory.setPnrDataHistoryType(pnrDataHistoryType);
        pnrDataHistory.setManager(manager);
        pnrDataHistory.setCreateDate(new Date());
        this.pnrDataHistoryRepo.insert(pnrDataHistory);
        /* #endregion */

        /* #region save travel status history */
        if (isNew) {
            this.insertTravelStatusHistory(userInfo, travel);
        }
        /* #endregion */

        this.saveTravelCities(isNew, travel.getId(), journeys);
        this.saveDocumentNumbers(userInfo, isNew, travel.getId(), btmsAdditionalInfo);
        this.saveBookingAirSchedules(isNew, bookingAir.getId(), journeys, passengerInfos);
    }

    private Company getCompany(Long oldCompanyId, PNRInfoRes pnrInfo) {
        BtmsAdditionalInfoRes btmsAdditionalInfo = pnrInfo.getRetrieveData().getBtmsAdditionalInfo();

        if (btmsAdditionalInfo != null && !StringUtils.isNullOrEmpty(btmsAdditionalInfo.getCompanyCd())) { // Have a company sitecode
            String companySiteCode = btmsAdditionalInfo.getCompanyCd();
            Company company = this.companyRepo.findTop1ByIsDeletedFalseAndSiteCode(companySiteCode);
            // Don't exist company with sitecode
            if (company == null) {
                throw new RuntimeException("Don't exist company with sitecode " + companySiteCode);
            }
            return company;
        }
        if (oldCompanyId != null) { // Already exist travel
            return this.companyRepo.findById(oldCompanyId);
        }
        return this.companyRepo.findById(Constants.COMPANY_ID_WITHOUT_SITE_CODE);
    }

    public String getTravelPlace(List<JourneyRes> journeys) {
        JourneyRes firstJourney = journeys.getFirst();
        String travelPlace = firstJourney.getArrAirport();

        if (journeys.size() == 1) {
            return travelPlace;
        }

        String firstDepartureDate = firstJourney.getDepartureDate().substring(0, 10);

        for (int i = 1; i < journeys.size(); i++) {
            JourneyRes journey = journeys.get(i);
            if (journey.getDepartureDate().substring(0, 10).equals(firstDepartureDate)) {
                travelPlace = journey.getArrAirport();
            } else {
                break;
            }
        }

        return travelPlace;
    }

    private List<Customer> getCustomerTravelers(Company company, List<PassengerInfoRes> passengerInfos) {
        List<Customer> customers = new ArrayList<>();

        for (PassengerInfoRes passengerInfo : passengerInfos) {
            Customer customer = this.customerRepo.findByCompanyIdAndFirstNameAndLastName(company.getId(), passengerInfo.getName().getFirstName(), passengerInfo.getName().getLastName());
            customers.add(customer);
        }

        return customers;
    }

    private Customer getCustomerReserver(Company company, Contact contact) {
        if (contact != null && contact.getName() != null) {
            return this.customerRepo.findByCompanyIdAndName(company.getId(), contact.getName().getFirstName() + contact.getName().getLastName());
        }

        return null;
    }

    public void saveTravelCities(boolean isNew, Long travelId, List<JourneyRes> journeys) {
        List<Long> cityIds = new ArrayList<>();

        for (int i = 0; i < journeys.size(); i++) {
            JourneyRes journey = journeys.get(i);
            if (i > 0) {
                Airport deptAirport = this.airportService.findOrInsertByCode(journey.getDeptAirport());
                Long deptCityId = deptAirport.getCityId();
                if (deptCityId != null && (cityIds.isEmpty() || !cityIds.getLast().equals(deptCityId))) {
                    cityIds.add(deptCityId);
                }
            }
            Airport arrAirport = this.airportService.findOrInsertByCode(journey.getArrAirport());
            Long arrCityId = arrAirport.getCityId();
            if (arrCityId != null && (cityIds.isEmpty() || !cityIds.getLast().equals(arrCityId))) {
                cityIds.add(arrCityId);
            }
        }

        // Delete all travel cities
        if (!isNew) {
            this.travelCityRepo.deleteByTravelId(travelId);
        }

        // Insert new travel cities
        for (Long cityId : cityIds) {
            TravelCity travelCity = new TravelCity();
            travelCity.setTravelId(travelId);
            travelCity.setTravelCityId(cityId);
            this.travelCityRepo.insert(travelCity);
        }
    }

    public void saveBookingAirSchedules(boolean isNew, Long bookingAirId, List<JourneyRes> journeys, List<PassengerInfoRes> passengerInfos) {
        List<BookingAirSchedule> bookingAirSchedules = new ArrayList<>();
        int scheduleSeqNo = 1;
        for (JourneyRes journey : journeys) {
            for (int i = 0; i < journey.getSegments().size(); i++) {
                SegmentRes segment = journey.getSegments().get(i);
                BookingAirSchedule bookingAirSchedule = new BookingAirSchedule();
                bookingAirSchedule.setBookingAirId(bookingAirId);
                Airline airline = this.airlineService.findOrInsertByCode(segment.getCarrierCode());
                bookingAirSchedule.setAirlineId(airline.getId());
                Airline opAirline = this.airlineService.findOrInsertByCode(segment.getLegs().getFirst().getOperatingCarrier());
                bookingAirSchedule.setOpAirlineId(opAirline.getId());
                bookingAirSchedule.setOpAirlineText(opAirline.getNameEng());
                Airport fromAirport = this.airportService.findOrInsertByCode(segment.getDeptAirport());
                bookingAirSchedule.setFromAirportId(fromAirport.getId());
                Airport toAirport = this.airportService.findOrInsertByCode(segment.getArrAirport());
                bookingAirSchedule.setToAirportId(toAirport.getId());
                bookingAirSchedule.setAirlineFlightNo(segment.getFlightNumber());
                bookingAirSchedule.setDepartureTerminal(segment.getLegs().getFirst().getDepartureTerminal());
                bookingAirSchedule.setArrivalTerminal(segment.getLegs().getLast().getArrivalTerminal());
                bookingAirSchedule.setFromDate(DateUtil.string2Date(segment.getDepartureDate(), "yyyy-MM-dd'T'HH:mm:ss"));
                bookingAirSchedule.setToDate(DateUtil.string2Date(segment.getArrivalDate(), "yyyy-MM-dd'T'HH:mm:ss"));
                bookingAirSchedule.setSeatCountAdult(passengerInfos.size());
                bookingAirSchedule.setSeatType(segment.getCabinClass());
                bookingAirSchedule.setBookingClassCode(segment.getBookingClass());
                bookingAirSchedule.setGdsBookingStatusCode(segment.getStatus());
                bookingAirSchedule.setLeadTime(DateUtil.formatMinuteHHmm(segment.getFlightTime()));
                if (i == 0) {
                    bookingAirSchedule.setVia(false);
                    bookingAirSchedule.setGroundTime("0000");
                } else {
                    SegmentRes preSegment = journey.getSegments().get(i - 1);
                    bookingAirSchedule.setVia(true);
                    bookingAirSchedule.setGroundTime(DateUtil.formatMinuteHHmm(preSegment.getWaitingTime()));
                }
                bookingAirSchedule.setBaggageAllow(passengerInfos.getFirst().getBaggageAllowanceMap().get(journey.getJourneyKey()));
                bookingAirSchedule.setScheduleSeqNo(scheduleSeqNo++);

                bookingAirSchedules.add(bookingAirSchedule);
            }
        }

        // Delete all bookingair schedules
        if (!isNew) {
            this.bookingAirScheduleRepo.deleteByBookingAirId(bookingAirId);
        }

        // Insert new bookingair schedules
        for (BookingAirSchedule bookingAirSchedule : bookingAirSchedules) {
            this.bookingAirScheduleRepo.insert(bookingAirSchedule);
        }
    }

    private void saveDocumentNumbers(UserInfo userInfo, boolean isNew, Long travelId, BtmsAdditionalInfoRes btmsAdditionalInfo) {
        if (btmsAdditionalInfo == null || btmsAdditionalInfo.getDocumentNumber().size() == 0) {
            return;
        }
        // Delete all document numbers
        if (!isNew) {
            this.documentNumberRepo.deleteByBookingIdAndBookingType(travelId, BookingType.AIR);
        }

        for (int i = 0; i < btmsAdditionalInfo.getDocumentNumber().size(); i++) {
            DocumentNumber documentNumber = new DocumentNumber();
            documentNumber.setBookingId(travelId);
            documentNumber.setBookingType(BookingType.AIR);
            documentNumber.setDocumentNo(btmsAdditionalInfo.getDocumentNumber().get(i));
            documentNumber.setOrderNo(Long.valueOf(i + 1));
            if (userInfo != null) {
                documentNumber.setCreatorUserId(userInfo.getUserId());
            }

            // Insert new document numbers
            this.documentNumberRepo.insert(documentNumber);
        }
    }

    public BusinessTrip upsertBusinessTrip(Long oldBusinessTripId, List<JourneyRes> journeys) {
        BusinessTrip businessTrip = new BusinessTrip();

        String businessTripName = "출장";
        Airport arrAirport = this.airportService.findByCode(journeys.getFirst().getArrAirport());
        if (arrAirport != null) {
            City arrCity = this.cityService.findById(arrAirport.getCityId());
            if (arrCity != null) {
                businessTripName = arrCity.getName() + " 출장";
            }
        }
        if (!StringUtils.isNullOrEmpty(journeys.getFirst().getDepartureDate())) {
            businessTripName += (" " + journeys.getFirst().getDepartureDate().substring(0, 10));
            businessTrip.setFromDate(DateUtil.string2Date(journeys.getFirst().getDepartureDate(), "yyyy-MM-dd'T'HH:mm:ss"));
        }
        if (!StringUtils.isNullOrEmpty(journeys.getLast().getArrivalDate())) {
            businessTripName += ("~" + journeys.getLast().getArrivalDate().substring(0, 10));
            businessTrip.setToDate(DateUtil.string2Date(journeys.getLast().getArrivalDate(), "yyyy-MM-dd'T'HH:mm:ss"));
        }

        businessTrip.setBusinessTripName(businessTripName);

        if (oldBusinessTripId != null) {
            businessTrip.setId(oldBusinessTripId);
            this.businessTripRepo.update(businessTrip);
        } else {
            this.businessTripRepo.insert(businessTrip);
        }

        return businessTrip;
    }

    public void insertTravelStatusHistory(UserInfo userInfo, Travel travel) {
        TravelStatusHistory travelStatusHistory = new TravelStatusHistory();
        travelStatusHistory.setTravelId(travel.getId());
        travelStatusHistory.setStatus(travel.getStatus());
        travelStatusHistory.setTravelBookingType(travel.getTravelBookingType());
        travelStatusHistory.setModifyInfo(travel.getStatus().getModifyInfo());
        travelStatusHistory.setModifyDate(new Date());
        if (userInfo != null) {
            travelStatusHistory.setModifier(new User(userInfo.getUserId()));
        }
        this.travelStatusHistoryRepo.insert(travelStatusHistory);
    }

    private boolean getIsOverseas(List<JourneyRes> journeys) {
        for (JourneyRes journey : journeys) {
            if (journey.isOverseas()) {
                return true;
            }
        }

        return false;
    }

    private Date getBookingDate(GDSType gdsType, String pnrRawData) {
        if (gdsType.equals(GDSType.AMADEUS)) {
            Pattern pattern = Pattern.compile("[0-9]{1,2}(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)[0-9]{2}\\/[0-9]{4}Z");
            Matcher matcher = pattern.matcher(pnrRawData);
            if (matcher.find()) {
                String bookingDateStr = matcher.group();
                DateTimeFormatter formatter = new DateTimeFormatterBuilder().parseCaseInsensitive().appendPattern("dMMMyy/HHmm'Z'").toFormatter();
                ZonedDateTime zonedDateTime = ZonedDateTime.of(LocalDateTime.parse(bookingDateStr, formatter), ZoneOffset.UTC);
                return Date.from(zonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toInstant());
            }
        } else if (gdsType.equals(GDSType.SABRE)) {
            Pattern pattern = Pattern.compile("[0-9]{4}\\/[0-9]{1,2}(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)[0-9]{2}");
            Matcher matcher = pattern.matcher(pnrRawData);
            if (matcher.find()) {
                String bookingDateStr = matcher.group();
                DateTimeFormatter formatter = new DateTimeFormatterBuilder().parseCaseInsensitive().appendPattern("HHmm/dMMMyy").toFormatter();
                ZonedDateTime zonedDateTime = ZonedDateTime.of(LocalDateTime.parse(bookingDateStr, formatter), ZoneOffset.UTC);
                return Date.from(zonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toInstant());
            }
        }

        return null;
    }

    private String getOfficeId(InflowBookingType inflowBookingType, boolean isOverseas, List<JourneyRes> journeys) {
        if (inflowBookingType.equals(InflowBookingType.CRS)) {
            if (isOverseas) {
                if (journeys.getFirst().getProvider().equals(GDSType.AMADEUS)) {
                    return Constants.AMADEUS_OFFLINE_OID;
                } else if (journeys.getFirst().getProvider().equals(GDSType.SABRE)) {
                    return Constants.SABRE_OFFLINE_PCC;
                }
            } else {
                if (journeys.getFirst().getAirline().equals("KE")) {
                    return Constants.KE_OFFLINE_OID;
                } else if (journeys.getFirst().getAirline().equals("OZ")) {
                    return Constants.OZ_OFFLINE_PCC;
                }
            }
        } else if (inflowBookingType.equals(InflowBookingType.BTMS)) {
            if (isOverseas) {
                if (journeys.getFirst().getProvider().equals(GDSType.AMADEUS)) {
                    return Constants.AMADEUS_ONLINE_OID;
                } else if (journeys.getFirst().getProvider().equals(GDSType.SABRE)) {
                    return Constants.SABRE_ONLINE_PCC;
                }
            } else {
                if (journeys.getFirst().getAirline().equals("KE")) {
                    return Constants.KE_ONLINE_OID;
                } else if (journeys.getFirst().getAirline().equals("OZ")) {
                    return Constants.OZ_ONLINE_PCC;
                }
            }
        }

        return "";
    }

    private TravelAgencyUser getManager(BtmsAdditionalInfoRes btmsAdditionalInfo) {
        if (btmsAdditionalInfo == null) {
            return null;
        }
        if (StringUtils.isNullOrEmpty(btmsAdditionalInfo.getTsNumber())) {
            return null;
        }
        return this.travelAgencyUserRepo.findAndFetchDepartmentByEmployeeNoAndJoinStatusApprovalAndSineCode4AmadeusNotNull(btmsAdditionalInfo.getTsNumber());
    }

    public void saveBookingHistory(Long travelId, Long bookingAirId, Long modifierId, String fieldName, String fieldKrName, String originValue, String changeValue) {
        originValue = defaultIfEmpty(originValue, "");
        changeValue = defaultIfEmpty(changeValue, "");

        if (originValue.equals(changeValue)) {
            return;
        }
        BookingHistory bookingHistory = BookingHistory.builder()
                .travelId(travelId)
                .bookingAirId(bookingAirId)
                .modifierId(modifierId)
                .fieldName(fieldName)
                .fieldKrName(fieldKrName)
                .originValue(originValue)
                .changeValue(changeValue)
                .build();

        this.bookingHistoryRepo.insert(bookingHistory);
    }

    private String defaultIfEmpty(String str, String defaultStr) {
        return str == null || str.length() == 0 ? defaultStr : str;
    }
}
