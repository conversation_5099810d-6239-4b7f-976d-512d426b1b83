package com.tidesquare.btms.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tidesquare.btms.entity.Airport;
import com.tidesquare.btms.repository.AirportRepo;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@Startup
@ApplicationScoped
public class AirportService {
    private Map<String, Airport> codeToAirportMap;
    private Map<Long, Airport> idToAirportMap;

    @Inject
    private AirportRepo airportRepo;

    private static AirportService instance;

    public AirportService() {
        instance = this;
    }

    @PostConstruct
    void init() {
        List<Airport> airports = this.airportRepo.findAll();
        this.codeToAirportMap = new HashMap<>();
        this.idToAirportMap = new HashMap<>();
        for (Airport airport : airports) {
            codeToAirportMap.put(airport.getCode(), airport);
            idToAirportMap.put(airport.getId(), airport);
        }
    }

    public Airport findById(Long id) {
        return this.idToAirportMap.get(id);
    }

    public static Airport findByIdStatic(Long id) {
        return instance.findById(id);
    }

    public Airport findByCode(String code) {
        return this.codeToAirportMap.get(code);
    }

    public Airport findByCodeAndIsUseTrue(String code) {
        Airport airport = this.codeToAirportMap.get(code);
        return airport != null && airport.getIsUse() ? airport : null;
    }

    public Airport findOrInsertByCode(String code) {
        Airport airport = this.codeToAirportMap.get(code);
        if (airport == null) {
            airport = new Airport();
            airport.setCode(code);
            airport.setName(code);
            airport.setNameEng(code);
            airport.setIsUse(true);
            this.airportRepo.insert(airport);
            codeToAirportMap.put(airport.getCode(), airport);
            idToAirportMap.put(airport.getId(), airport);
        }
        return airport;
    }
}
