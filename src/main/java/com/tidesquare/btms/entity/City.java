package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.DomesticArea;
import com.tidesquare.btms.entity.converter.DomesticAreaConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "City")
@NoArgsConstructor
public class City {
	public City(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "cityId")
	private Long id;

	@Column(name = "countryId", nullable = false)
	private Long countryId;

	@Column(length = 10)
	private String cityCode;

	@Column(length = 100)
	private String name;

	@Column(length = 100)
	private String nameEng;

	@Column(length = 200)
	private String nameSynonym1;

	@Column(length = 200)
	private String nameSynonym2;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isOverseas = true;

	@Column(name = "domesticAreaCodeId")
	@Convert(converter = DomesticAreaConverter.class)
	private DomesticArea domesticArea;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isUse = true;
}