package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "travel_rule_air")
public class TravelRuleAir {
    @Id
    @Column(name = "TRAVEL_RULE_ID")
    private Long travelRuleId;

    @Id
    @Column(name = "POSITION_ID")
    private Long positionId;
}
